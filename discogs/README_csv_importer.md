# CSV到MongoDB批量导入工具使用说明

## 概述

`csv_to_mongodb_importer.py` 是一个专门用于将CSV文件中的release数据批量导入到MongoDB数据库的工具。该工具具有完善的错误处理、进度显示和数据验证功能。

## 功能特性

- ✅ **内存效率处理**：分批读取和插入，避免内存溢出
- ✅ **完善的错误处理**：包含重试机制和详细的错误日志
- ✅ **重复数据处理**：支持跳过或允许重复数据
- ✅ **进度显示**：实时显示处理进度和统计信息
- ✅ **数据类型转换**：自动处理JSON字段、数值字段和时间戳
- ✅ **测试模式**：支持小批量测试以验证功能
- ✅ **详细日志**：完整的操作日志和统计报告

## 安装依赖

确保已安装必要的Python包：

```bash
pip install pymongo
```

## 使用方法

### 基本用法

```bash
# 使用默认设置导入CSV文件
python csv_to_mongodb_importer.py

# 指定CSV文件路径
python csv_to_mongodb_importer.py --csv-file api_releases_补全_20250729_153950.csv
```

### 高级选项

```bash
# 测试模式（只处理前100行）
python csv_to_mongodb_importer.py --test-mode

# 自定义批量大小
python csv_to_mongodb_importer.py --batch-size 500

# 允许重复数据（默认跳过重复）
python csv_to_mongodb_importer.py --allow-duplicates

# 自定义日志文件
python csv_to_mongodb_importer.py --log-file my_import.log

# 组合使用多个选项
python csv_to_mongodb_importer.py \
    --csv-file my_data.csv \
    --batch-size 200 \
    --test-mode \
    --log-file test_import.log
```

### 命令行参数说明

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--csv-file` | `-f` | `api_releases_补全_20250729_153950.csv` | CSV文件路径 |
| `--batch-size` | `-b` | `100` | 批量插入大小 |
| `--test-mode` | `-t` | `False` | 测试模式，只处理前100行 |
| `--allow-duplicates` | `-d` | `False` | 允许重复数据 |
| `--log-file` | `-l` | `csv_import_log.txt` | 日志文件路径 |

## CSV文件格式要求

工具期望CSV文件包含以下字段：

### 必需字段
- `id`: 记录ID（整数）
- `y_id`: 内部ID（字符串）

### 可选字段
- `title`: 标题
- `artists`: 艺术家信息（JSON数组）
- `extra_artists`: 额外艺术家信息（JSON数组）
- `labels`: 标签信息（JSON数组）
- `companies`: 公司信息（JSON数组）
- `country`: 国家
- `formats`: 格式信息（JSON数组）
- `genres`: 流派（JSON数组）
- `styles`: 风格（JSON数组）
- `identifiers`: 标识符（JSON数组）
- `tracklist`: 曲目列表（JSON数组）
- `master_id`: 主版本ID（整数）
- `discogs_status`: Discogs状态
- `images`: 图片信息（JSON数组）
- `notes`: 备注
- `year`: 年份（整数）
- `images_permissions`: 图片权限（整数）
- `permissions`: 权限（整数）
- `source`: 数据源（整数）
- `created_at`: 创建时间（ISO格式）
- `updated_at`: 更新时间（ISO格式）

## 数据库配置

工具使用以下环境变量进行数据库连接：

```bash
export MONGO_URI="**********************************************************"
export DB_NAME="music_test"
```

或者直接修改脚本中的默认配置。

## 输出和日志

### 控制台输出
工具会在控制台显示：
- 连接状态
- 处理进度
- 最终统计信息

### 日志文件
详细的操作日志会保存到指定的日志文件中，包括：
- 每个操作的时间戳
- 错误详情
- 数据转换问题
- 最终统计报告

### 统计信息
导入完成后会显示：
- 总处理行数
- 成功插入数量
- 失败插入数量
- 跳过的重复记录数
- 验证错误数量
- 处理时间
- 成功率

## 错误处理

### 常见错误及解决方案

1. **MongoDB连接失败**
   - 检查数据库服务是否运行
   - 验证连接字符串和认证信息
   - 确认网络连接

2. **CSV文件读取错误**
   - 检查文件路径是否正确
   - 确认文件编码为UTF-8
   - 验证文件权限

3. **JSON解析错误**
   - 检查CSV中的JSON字段格式
   - 确认双引号转义正确
   - 查看日志中的具体错误信息

4. **数据验证失败**
   - 确保必需字段（id, y_id）不为空
   - 检查数据类型是否正确

## 性能优化建议

1. **批量大小调整**
   - 对于大文件，可以增加批量大小（如500-1000）
   - 对于内存受限环境，减少批量大小

2. **测试模式使用**
   - 在处理大文件前，先使用测试模式验证
   - 确认数据格式和转换逻辑正确

3. **重复数据处理**
   - 如果确定没有重复数据，使用 `--allow-duplicates` 可以提高性能
   - 对于可能有重复的数据，保持默认的跳过重复设置

## 示例场景

### 场景1：首次导入
```bash
# 先测试少量数据
python csv_to_mongodb_importer.py --test-mode

# 确认无误后导入全部数据
python csv_to_mongodb_importer.py
```

### 场景2：增量导入
```bash
# 允许重复数据，用于增量更新
python csv_to_mongodb_importer.py --allow-duplicates
```

### 场景3：大文件处理
```bash
# 增加批量大小以提高性能
python csv_to_mongodb_importer.py --batch-size 1000
```

## 故障排除

如果遇到问题，请：

1. 查看日志文件中的详细错误信息
2. 使用测试模式验证小批量数据
3. 检查CSV文件格式和编码
4. 验证数据库连接和权限
5. 确认MongoDB服务状态

## 注意事项

- 确保在导入前备份重要数据
- 大文件导入可能需要较长时间，请耐心等待
- 建议在非高峰时段进行大批量导入
- 定期检查日志文件以监控导入状态
