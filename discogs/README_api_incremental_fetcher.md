# Discogs API 增量数据获取器

## 概述

这是一个专门用于通过 Discogs API 增量获取 release 数据的 Python 脚本。脚本会自动从数据库中查询最大的 ID 值，然后从该 ID+1 开始依次调用 API 获取数据，并保存到 CSV 文件中。

## 核心功能

1. **智能起始点检测**：自动查询数据库中最大的 release ID，从下一个 ID 开始获取
2. **API 频率限制处理**：严格遵守 Discogs API 的频率限制（每分钟 60 次请求）
3. **连续 404 停止机制**：当连续遇到 20 次 404 错误时自动停止
4. **断点续传功能**：支持从上次中断的位置继续处理
5. **批量 CSV 输出**：数据格式与现有项目保持一致
6. **详细进度跟踪**：实时显示处理进度和统计信息

## 文件结构

```
discogs/
├── api_incremental_fetcher.py          # 主脚本
├── test_incremental_fetcher.py         # 测试脚本
├── README_api_incremental_fetcher.md   # 使用说明（本文件）
├── api_incremental_progress.json       # 进度文件（运行时生成）
├── api_incremental_log.txt            # 日志文件（运行时生成）
└── api_releases_补全_YYYYMMDD_HHMMSS.csv # CSV输出文件（运行时生成）
```

## 环境要求

### Python 依赖
- Python 3.7+
- pymongo
- requests
- 现有的 `api_release_补全器.py` 模块

### 数据库要求
- MongoDB 连接
- `release_new` 集合存在且包含数据

### API 要求
- Discogs API 访问权限
- 网络连接稳定

## 配置参数

脚本支持通过环境变量进行配置：

```bash
# 数据库配置
export MONGO_URI="**********************************************************"
export DB_NAME="music_test"

# 处理配置
export MAX_CONSECUTIVE_404="20"    # 连续404停止阈值
export BATCH_SIZE="100"            # CSV批量写入大小
export MAX_RECORDS="0"             # 最大处理记录数，0表示无限制
```

## 使用方法

### 1. 基本使用

```bash
cd discogs
python api_incremental_fetcher.py
```

### 2. 运行前测试

建议先运行测试脚本确保环境正常：

```bash
python test_incremental_fetcher.py
```

### 3. 断点续传

如果脚本中断，再次运行时会询问是否从上次进度继续：

```
📂 发现进度文件，是否从上次进度继续？(y/n): y
```

## 输出文件

### 1. CSV 数据文件
- 文件名格式：`api_releases_补全_YYYYMMDD_HHMMSS.csv`
- 包含字段：id, y_id, title, artists, extra_artists, labels, companies, country, formats, genres, styles, identifiers, tracklist, master_id, discogs_status, images, notes, year, images_permissions, permissions, source, created_at, updated_at
- 数组字段以 JSON 格式存储

### 2. 进度文件
- 文件名：`api_incremental_progress.json`
- 包含当前处理状态、统计信息等
- 用于断点续传功能

### 3. 日志文件
- 文件名：`api_incremental_log.txt`
- 包含详细的运行日志和错误信息

## 工作流程

1. **初始化阶段**
   - 验证环境（数据库连接、API客户端等）
   - 查询数据库中最大的 release ID
   - 设置起始处理 ID（最大ID + 1）

2. **数据获取阶段**
   - 依次调用 Discogs API 获取 release 数据
   - 处理 API 响应（成功/404/错误）
   - 转换数据格式并批量写入 CSV

3. **停止条件**
   - 连续遇到 20 次 404 错误
   - 达到最大处理记录数限制（如果设置）
   - 用户手动中断（Ctrl+C）

## 错误处理

### API 错误处理
- **404 错误**：记录并跳过，累计连续 404 计数
- **429 限流错误**：自动等待 60 秒后重试
- **网络超时**：最多重试 3 次
- **其他错误**：记录错误并继续处理下一个 ID

### 数据库错误处理
- 连接失败时自动重试
- 查询超时时使用默认值
- 写入失败时记录错误但不中断处理

## 性能特点

### 处理速度
- API 限制：每分钟最多 60 次请求
- 实际速度：约 1 请求/秒（考虑网络延迟）
- 批量写入：每 100 条记录写入一次 CSV

### 内存使用
- 流式处理，内存占用稳定
- 批量缓存最多 100 条记录
- 不会因数据量增大而内存溢出

### 网络优化
- 自动重试机制
- 智能频率控制
- 连接池复用

## 监控和统计

脚本会定期显示处理进度：

```
📊 进度报告:
   当前ID: 34419650
   已处理: 100
   成功获取: 85
   404跳过: 12
   错误: 3
   连续404: 2
   处理速度: 0.95 ID/秒
   运行时间: 1.8 分钟
```

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: No module named 'api_release_补全器'
   ```
   - 确保 `api_release_补全器.py` 文件存在
   - 检查文件路径和权限

2. **数据库连接失败**
   ```
   MongoDB连接失败: ServerSelectionTimeoutError
   ```
   - 检查网络连接
   - 验证数据库地址和凭据
   - 确认数据库服务正常运行

3. **API 调用失败**
   ```
   API客户端初始化失败
   ```
   - 检查网络连接
   - 验证 Discogs API 访问权限
   - 确认 API 服务状态

### 调试模式

可以通过修改日志级别来获取更详细的调试信息：

```python
logging.basicConfig(level=logging.DEBUG)
```

## 最佳实践

1. **运行前准备**
   - 先运行测试脚本验证环境
   - 确保有足够的磁盘空间存储 CSV 文件
   - 检查网络连接稳定性

2. **长期运行**
   - 使用 screen 或 tmux 在后台运行
   - 定期检查日志文件
   - 监控磁盘空间使用

3. **数据管理**
   - 定期备份 CSV 文件
   - 清理旧的日志文件
   - 监控数据库性能

## 技术细节

### 数据转换
- 使用现有的 `convert_api_response_to_document` 函数
- 保持与项目其他模块的数据格式一致
- 自动生成 YRD 编号

### CSV 格式
- 使用 UTF-8 编码
- 数组字段以 JSON 字符串存储
- 日期字段使用 ISO 格式

### 进度保存
- JSON 格式存储进度信息
- 包含详细的统计数据
- 支持断点续传

## 版本信息

- 版本：1.0.0
- 创建日期：2025-07-29
- 作者：AI Assistant
- 兼容性：Python 3.7+, MongoDB 4.0+

## 许可证

本脚本遵循项目的整体许可证协议。
