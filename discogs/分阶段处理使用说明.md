# Discogs API Release 数据补全器 - 分阶段处理使用说明

## 📋 概述

分阶段处理模式是为了解决大规模数据处理中的内存和时间问题而设计的。它将原本需要一次性处理的 3400 万个 ID 分解为三个独立的阶段，每个阶段都可以独立执行和恢复。

## 🎯 解决的问题

### 原有问题

1. **内存不足**：一次性加载 3400 万个 ID 需要大量内存
2. **处理时间长**：检测缺失 ID 需要 10-20 分钟，容易被中断
3. **无法恢复**：中断后需要重新开始，浪费时间
4. **难以并行**：无法分割任务进行并行处理

### 解决方案

1. **分段检测**：将 ID 范围分割成小段（默认 10 万个 ID/段）
2. **批次处理**：将缺失 ID 分组成可管理的批次（默认 5000 个 ID/批次）
3. **断点续传**：每个阶段都支持中断后继续
4. **独立执行**：每个批次可以独立处理

## 🔧 配置参数

### 环境变量配置

```bash
# 分阶段处理模式开关
export STAGED_MODE="true"          # 启用分阶段处理（默认：true）

# 分段检测配置（优先使用MAX_SEGMENTS）
export MAX_SEGMENTS="10"           # 最大段数，动态计算每段大小（默认：10）
export SEGMENT_SIZE="100000"       # 固定段大小，当MAX_SEGMENTS=0时使用（默认：100000）

# 批次处理配置
export PROCESSING_BATCH_SIZE="5000" # 每批次处理的缺失ID数量（默认：5000）
export MAX_CONCURRENT_BATCHES="1"   # 最大并发批次数（默认：1）

# 测试模式配置
export TEST_MODE="true"            # 测试模式（默认：false）
export MAX_RECORDS="50"            # 测试模式最大处理记录数
```

### 推荐配置

#### 生产环境（推荐）

```bash
export STAGED_MODE="true"
export MAX_SEGMENTS="10"           # 分成10段，每段约340万ID
export PROCESSING_BATCH_SIZE="5000" # 5000ID/批次，适合API频率限制
export TEST_MODE="false"
```

#### 高内存环境（可选）

```bash
export STAGED_MODE="true"
export MAX_SEGMENTS="5"            # 分成5段，每段约680万ID
export PROCESSING_BATCH_SIZE="10000" # 更大的批次
export TEST_MODE="false"
```

#### 测试环境

```bash
export STAGED_MODE="true"
export MAX_SEGMENTS="3"            # 小段数测试
export PROCESSING_BATCH_SIZE="10"  # 小批次测试
export TEST_MODE="true"
export MAX_RECORDS="50"
```

## 📊 处理阶段详解

### 阶段 1: 分段检测缺失 ID

**功能**：将 3400 万个 ID 范围分割成段，逐段检测缺失的 ID

**特点**：

- 默认分成 10 段，每段约 340 万个 ID（可配置）
- 支持断点续传，中断后从上次位置继续
- 内存使用量大幅优化，每段约 130MB（相比原来的 1.3GB）
- 进度实时保存，不会丢失已检测的结果

**输出文件**：

- `missing_ids_segments.json` - 所有缺失的 ID 列表
- `segment_progress.json` - 分段检测进度

**示例输出**：

```
🔍 开始分段检测缺失的release ID...
📊 数据库中最大ID: 34419592
📊 使用最大段数模式: 10 段, 每段约 3,442,000 个ID
📊 总ID数量: 34,419,592, 分为 10 段处理
🔄 检测段 1/10 (ID: 1-3442000)
✅ 段 1 完成: 缺失 2,456,789 个ID
💾 已保存 2,456,789 个缺失ID到文件
```

### 阶段 2: 创建处理批次

**功能**：将检测到的缺失 ID 分割成可管理的批次

**特点**：

- 每批次 5000 个 ID（可配置）
- 每个批次独立配置，支持并行处理
- 批次信息持久化保存

**输出文件**：

- `batch_config.json` - 批次配置信息

**示例输出**：

```
📦 开始创建处理批次，总ID数: 26,484,533
✅ 创建了 5,297 个批次，每批次最多 5000 个ID
💾 已保存批次配置: 5,297 个批次
```

### 阶段 3: 分批 API 调用

**功能**：对每个批次分别调用 Discogs API 获取数据

**特点**：

- 每个批次独立处理，支持断点续传
- 每个批次生成独立的 CSV 文件
- 详细的进度跟踪和错误处理
- 支持并行处理多个批次

**输出文件**：

- `batch_1_releases.csv` - 批次 1 的 CSV 输出
- `batch_2_releases.csv` - 批次 2 的 CSV 输出
- `batch_progress/batch_1_progress.json` - 批次 1 的进度文件

**示例输出**：

```
🚀 开始处理批次 1: 5000 个ID
📊 ID范围: 1 - 5000
📊 批次 1 进度: 1000/5000 (成功: 856, 404: 144, 错误: 0)
✅ 批次 1 处理完成
📊 最终统计: 处理 5000, 成功 4234, 404跳过 766, 错误 0
```

## 🚀 使用方法

### 1. 基本使用

```bash
# 启动分阶段处理
export STAGED_MODE="true"
python3 api_release_补全器.py
```

### 2. 测试模式

```bash
# 小规模测试
export STAGED_MODE="true"
export TEST_MODE="true"
export SEGMENT_SIZE="1000"
export PROCESSING_BATCH_SIZE="10"
python3 api_release_补全器.py
```

### 3. 功能测试

```bash
# 运行分阶段处理测试
python3 test_staged_processing.py
```

### 4. 单独执行某个阶段

如果需要单独执行某个阶段，可以修改代码或使用不同的环境变量：

```bash
# 只执行阶段1（检测缺失ID）
# 修改main_staged()函数，注释掉阶段2和3的代码

# 只执行阶段3（处理已有的批次）
# 确保batch_config.json文件存在，直接运行批次处理部分
```

## 📁 文件结构

### 生成的文件

```
discogs/
├── missing_ids_segments.json      # 缺失ID列表
├── segment_progress.json          # 分段检测进度
├── batch_config.json             # 批次配置
├── batch_progress/               # 批次进度目录
│   ├── batch_1_progress.json    # 批次1进度
│   ├── batch_2_progress.json    # 批次2进度
│   └── ...
├── batch_1_releases.csv         # 批次1输出
├── batch_2_releases.csv         # 批次2输出
├── ...
└── api_补全_log.txt             # 主日志文件
```

### 文件说明

- **missing_ids_segments.json**: 包含所有检测到的缺失 ID，按顺序排列
- **segment_progress.json**: 记录哪些段已经检测完成，支持断点续传
- **batch_config.json**: 批次配置信息，包含每个批次的 ID 列表和状态
- **batch_progress/**: 每个批次的详细进度，包含处理统计和最后处理的 ID
- **batch_X_releases.csv**: 每个批次的 CSV 输出文件

## 🔄 断点续传

### 分段检测续传

如果分段检测被中断，重新运行程序会：

1. 读取`segment_progress.json`文件
2. 跳过已完成的段
3. 从上次中断的位置继续

### 批次处理续传

如果批次处理被中断，重新运行程序会：

1. 读取`batch_progress/batch_X_progress.json`文件
2. 从上次处理的 ID 之后继续
3. 保持已有的统计数据

## 📊 性能优势

### 内存使用

- **原方案**: 需要加载 3400 万个 ID 到内存（约 1.3GB）
- **分阶段方案**: 每次只处理 10 万个 ID（约 4MB）

### 处理时间

- **原方案**: 检测阶段需要 10-20 分钟，中断后重新开始
- **分阶段方案**: 每段检测 1-2 分钟，中断后从断点继续

### 并行能力

- **原方案**: 无法并行处理
- **分阶段方案**: 可以同时处理多个批次（需要注意 API 频率限制）

## ⚠️ 注意事项

1. **API 频率限制**: Discogs API 限制 1 秒 1 次请求，并行处理时需要注意
2. **磁盘空间**: 每个批次会生成独立的 CSV 文件，需要足够的磁盘空间
3. **网络稳定性**: 长时间运行需要稳定的网络连接
4. **数据库连接**: 确保 MongoDB 连接稳定，避免超时

## 🛠️ 故障排除

### 常见问题

1. **分段检测中断**

   - 检查`segment_progress.json`文件是否存在
   - 重新运行程序会自动从断点继续

2. **批次处理失败**

   - 检查`batch_progress/`目录下的进度文件
   - 查看具体的错误日志

3. **CSV 文件缺失**

   - 检查批次是否包含有效数据（非 404 的 ID）
   - 查看批次进度文件中的统计信息

4. **内存不足**
   - 减小`SEGMENT_SIZE`参数
   - 减小`PROCESSING_BATCH_SIZE`参数

## 🎯 最佳实践

1. **生产环境建议**：

   - 使用默认的段大小（100,000）和批次大小（5,000）
   - 启用日志记录，便于监控进度
   - 定期备份进度文件

2. **测试环境建议**：

   - 使用小的段大小（1,000）和批次大小（10）
   - 启用测试模式，限制处理数量

3. **监控建议**：
   - 定期检查日志文件
   - 监控磁盘空间使用
   - 关注 API 调用成功率

这种分阶段处理模式大大提高了程序的稳定性和可维护性，特别适合处理大规模数据集。
