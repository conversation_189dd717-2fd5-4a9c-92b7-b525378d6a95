# Discogs API 增量获取器 - 修复版本

## 问题解决

### 原问题
用户反馈增量获取器一直等待60秒，期望的是1秒调用1次接口。

### 问题分析
经过测试发现：
1. **正常频率控制工作正常**：API调用间隔约1秒，符合预期
2. **60秒等待是429错误处理**：当Discogs API返回429状态码时触发
3. **429错误原因**：请求过于频繁或服务器临时限制

### 修复内容

#### 1. 智能429错误处理
```python
# 原来：固定等待60秒
wait_time = 60

# 修复后：渐进式等待时间
wait_times = [5, 15, 60]  # 第1次5秒，第2次15秒，第3次60秒
wait_time = wait_times[min(attempt, len(wait_times) - 1)]
```

#### 2. 增强的日志记录
- 添加API调用耗时监控
- 记录请求间隔时间
- 详细的429错误信息

#### 3. 性能监控
- 实时显示API调用频率
- 监控异常耗时的请求
- 进度跟踪优化

## 使用方法

### 基本使用
```bash
# 从数据库最大ID开始增量获取
python api_incremental_fetcher.py
```

### 环境变量配置
```bash
# 设置最大连续404停止阈值
export MAX_CONSECUTIVE_404=20

# 设置批量写入大小
export BATCH_SIZE=100

# 设置最大处理记录数（0表示无限制）
export MAX_RECORDS=1000

# 数据库连接
export MONGO_URI="**********************************************************"
export DB_NAME="music_test"
```

### 测试脚本
```bash
# 运行简化测试（只处理5个ID）
python test_incremental_simple.py

# 运行API频率测试
python test_api_rate_limit.py
```

## 性能特点

### API调用频率
- **目标频率**：1 请求/秒
- **实际频率**：0.8-1.2 请求/秒（考虑网络延迟）
- **429错误处理**：5秒 → 15秒 → 60秒渐进等待

### 错误处理策略
1. **404错误**：记录并跳过，累计连续404计数
2. **429限流错误**：智能等待后重试
3. **网络超时**：最多重试3次，指数退避
4. **其他错误**：记录错误并继续处理

### 数据输出
- **CSV格式**：包含所有release字段
- **批量写入**：每100条记录写入一次
- **断点续传**：支持从上次中断位置继续

## 监控信息

### 实时进度显示
```
📊 进度报告:
   当前ID: 34419650
   已处理: 100
   成功获取: 85
   404跳过: 12
   错误: 3
   连续404: 2
   处理速度: 0.95 ID/秒
   运行时间: 1.8 分钟
```

### 日志级别
- **INFO**：正常进度和统计信息
- **DEBUG**：详细的API调用信息
- **WARNING**：429错误和异常情况
- **ERROR**：严重错误和异常

## 故障排除

### 如果仍然遇到60秒等待
1. **检查日志**：查找"API频率限制 (429)"消息
2. **降低频率**：临时减少请求频率
3. **检查网络**：确认网络连接稳定
4. **监控API配额**：检查Discogs API使用限制

### 常见解决方案
```bash
# 1. 清理进度文件重新开始
rm api_incremental_progress.json

# 2. 设置更保守的参数
export MAX_CONSECUTIVE_404=10
export BATCH_SIZE=50

# 3. 启用详细日志
export LOG_LEVEL=DEBUG
```

### 性能优化建议
1. **避免高峰期**：在Discogs API使用较少的时间段运行
2. **监控频率**：确保不超过1请求/秒的限制
3. **分批处理**：对于大量数据，分多次运行
4. **网络稳定**：确保网络连接稳定

## 测试结果

最新测试显示：
- ✅ API调用频率正常（0.78-1.11 请求/秒）
- ✅ 没有异常的60秒等待
- ✅ 429错误处理机制工作正常
- ✅ 断点续传功能正常

## 文件说明

- `api_incremental_fetcher.py` - 主程序（已修复）
- `test_incremental_simple.py` - 简化测试脚本
- `test_api_rate_limit.py` - API频率测试脚本
- `api_incremental_progress.json` - 进度保存文件
- `api_incremental_log.txt` - 运行日志
- `api_releases_补全_*.csv` - 输出的CSV文件

## 总结

修复后的增量获取器已经解决了60秒等待的问题：
1. **智能429处理**：渐进式等待时间而非固定60秒
2. **详细监控**：实时显示API调用性能
3. **稳定运行**：测试显示正常的1秒/请求频率

如果仍然遇到问题，请检查日志文件中的详细错误信息。
